# @package _global_

defaults:
  - /habitat_baselines: habitat_baselines_rl_config_base
  - /habitat: habitat_config_base
  - /habitat/task: task_config_base
  - /habitat/task/actions:
    - stop
    - move_forward
    - turn_left
    - turn_right
    - look_up
    - look_down
    - subtask_stop
  - /habitat/task/measurements: 
    # success、spl 这些指标在 eval/test 时只是“打分”，不影响机器人实时决策。
    # agent 的行为只由 policy 决定，measurements 只是记录和统计。
    - distance_to_goal
    - goat_distance_to_goal_reward
    - success
    - spl
    - soft_spl
    - frontier_exploration_map # vlfm 只有这两个 measurements
    - traveled_stairs # vlfm 只有这两个 measurements
  - /habitat/task/lab_sensors:
    - compass_sensor
    - gps_sensor
    - heading_sensor # frontier_exploration/frontier_exploration/frontier_sensor.py
    # - frontier_sensor # frontier_exploration/frontier_exploration/frontier_sensor.py
    - base_explorer # frontier_exploration/frontier_exploration/base_explorer.py
    # - goat_goal_sensor # vlfm/config.py
    - goat_multi_goal_sensor
    - current_subtask_sensor # vlfm/config.py
  - /habitat/simulator/<EMAIL>.main_agent: rgbd_agent
  - /habitat/dataset: dataset_config_schema
  # - /habitat_baselines/rl/policy: goat_policy
  - _self_

habitat:
  env_task: GymGoatEnv
  
  environment:
    max_episode_steps: 5000 # 控制单个 episode 的长度
    iterator_options:
      max_scene_repeat_steps: 50000 # 控制单个 scene 内所有 episode 的总步数
  
  simulator:
    # type: GOATSim-v0 # 默认
    action_space_config: "v2-goat"
    turn_angle: 30
    tilt_angle: 30
    agents:
      main_agent: # agent 的 id
        height: 1.41
        radius: 0.17
        sim_sensors:
          rgb_sensor:
            hfov: 42
            height: 640
            width: 360
            position: [0, 1.31, 0]
          depth_sensor:
            min_depth: 0.5
            max_depth: 5.0
            hfov: 42
            height: 640
            width: 360
            position: [0, 1.31, 0]
    habitat_sim_v0:
      # gpu_device_id: 0 # 默认
      allow_sliding: False
    navmesh_settings:
      agent_max_climb: 0.1 # agent 能“爬”过的最大高度（单位：米）
      cell_height: 0.05 # 导航网格的高度精度 m

  task:
    reward_measure: goat_distance_to_goal_reward
    success_measure: success
    success_reward: 10.0
    slack_reward: -1e-3
    type: Goat-v1
    lab_sensors:
      base_explorer:
        turn_angle: 30
      # goat_goal_sensor:
      #   object_cache: data/goat-assets/goal_cache/ovon/category_name_clip_embeddings.pkl
      #   image_cache: data/goat-assets/goal_cache/iin/train_embeddings/
      #   image_cache_encoder: CLIP_goat
      #   language_cache: data/goat-assets/goal_cache/language_nav/train_instruction_clip_embeddings.pkl
    measurements:
      distance_to_goal:
        type: GoatDistanceToGoal
        distance_to: "VIEW_POINTS"
      spl:
        type: GoatSPL
      soft_spl:
        type: GoatSoftSPL
      success:
        type: GoatSuccess
        success_distance: 0.25 
    actions:
      stop:
        agent_index: 0
      move_forward:
        agent_index: 1
      turn_left:
        agent_index: 2
      turn_right:
        agent_index: 3
      look_up:
        agent_index: 4
      look_down:
        agent_index: 5
      subtask_stop:
        agent_index: 6

  dataset:
    type: Goat-v1
    split: val_unseen # 运行时中会改成和habitat_baselines.eval.split一致
    # scenes_dir: data/scene_datasets # 默认
    data_path: data/datasets/goat_bench/hm3d/v1/{split}/{split}.json.gz # episode dataset

habitat_baselines:
  evaluate: True
  trainer_name: "goat"
  # torch_gpu_id: 0
  # tensorboard_dir: "tb"
  # video_dir: "video_dir"
  # test_episode_count: -1
  eval_ckpt_path_dir: data/dummy_policy.pth
  num_environments: 1
  checkpoint_folder: "data/new_checkpoints"
  num_updates: 270000
  num_checkpoints: 100
  log_interval: 10
  # Force PyTorch to be single threaded as
  # this improves performance considerably
  force_torch_single_threaded: True
  load_resume_state_config: False

  eval:
    split: "val_unseen"
    # should_load_ckpt: True
    video_option: ["disk", "tensorboard"]
    
  rl:
    policy:
      name: "HabitatITMPolicyV2"

    ppo:
      # ppo params
      # clip_param: 0.2
      # ppo_epoch: 4
      # num_mini_batch: 2
      # value_loss_coef: 0.5
      # entropy_coef: 0.01
      # lr: 2.5e-4
      # eps: 1e-5
      max_grad_norm: 0.2
      num_steps: 64
      # use_gae: True
      # gamma: 0.99
      # tau: 0.95
      # use_linear_lr_decay: False
      # use_linear_clip_decay: False
      # reward_window_size: 50
      # use_normalized_advantage: False
      # hidden_size: 512

    ddppo:
      # sync_frac: 0.6
      # The PyTorch distributed backend to use
      distrib_backend: NCCL
      rnn_type: LSTM
      num_recurrent_layers: 2
      backbone: resnet50
      # Visual encoder backbone
      # pretrained_weights: data/ddppo-models/gibson-2plus-resnet50.pth
      # Initialize with pretrained weights
      # pretrained: False
      # Initialize just the visual encoder backbone with pretrained weights
      # pretrained_encoder: False
      # Whether or not the visual encoder backbone will be trained.
      # train_encoder: True
      # Whether or not to reset the critic linear layer
      reset_critic: False