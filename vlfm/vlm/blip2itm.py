# Copyright (c) 2023 Boston Dynamics AI Institute LLC. All rights reserved.

from typing import Any, Optional

import numpy as np
import torch
from PIL import Image

from .server_wrapper import ServerMixin, host_model, send_request, str_to_image

try:
    from lavis.models import load_model_and_preprocess
except ModuleNotFoundError:
    print("Could not import lavis. This is OK if you are only using the client.")


class BLIP2ITM:
    """BLIP 2 Image-Text Matching model."""

    def __init__(
        self,
        name: str = "blip2_image_text_matching",
        model_type: str = "pretrain",
        device: Optional[Any] = None,
    ) -> None:
        if device is None:
            device = torch.device("cuda") if torch.cuda.is_available() else "cpu"

        self.model, self.vis_processors, self.text_processors = load_model_and_preprocess(
            name=name,
            model_type=model_type,
            is_eval=True,
            device=device,
        )
        self.device = device

    def cosine(self, image: np.ndarray, txt: str) -> float:
        """
        Compute the cosine similarity between the image and the prompt.

        Args:
            image (numpy.ndarray): The input image as a numpy array.
            txt (str): The text to compare the image to.

        Returns:
            float: The cosine similarity between the image and the prompt.
        """
        pil_img = Image.fromarray(image)
        img = self.vis_processors["eval"](pil_img).unsqueeze(0).to(self.device)
        txt = self.text_processors["eval"](txt)
        with torch.inference_mode():
            cosine = self.model({"image": img, "text_input": txt}, match_head="itc").item()

        return cosine
    
    def get_image_embedding(self, image: np.ndarray) -> np.ndarray:
        """
        Extract the image embedding.
        
        Args:
            image (numpy.ndarray): The input image as a numpy array.
            
        Returns:
            numpy.ndarray: The image embedding vector.
        """
        pil_img = Image.fromarray(image)
        img = self.vis_processors["eval"](pil_img).unsqueeze(0).to(self.device)
        with torch.inference_mode():
            image_embeds = self.model.extract_features({"image": img}, mode="image").image_embeds
            # Normalize embedding
            image_embeds = image_embeds / image_embeds.norm(dim=-1, keepdim=True)
            
        return image_embeds.cpu().numpy().squeeze()
    
    def cosine_with_embedding(self, image: np.ndarray, embedding: np.ndarray) -> float:
        """
        Compute the cosine similarity between an image and a pre-computed embedding.
        
        Args:
            image (numpy.ndarray): The input image as a numpy array.
            embedding (numpy.ndarray): The pre-computed embedding vector.
            
        Returns:
            float: The cosine similarity between the image and the embedding.
        """
        # Get image embedding
        image_embedding = self.get_image_embedding(image)
        
        # Ensure embedding is normalized
        embedding_norm = embedding / np.linalg.norm(embedding)
        
        # Compute cosine similarity
        similarity = np.dot(image_embedding, embedding_norm)
        
        return float(similarity)


class BLIP2ITMClient:
    def __init__(self, port: int = 12182):
        self.url = f"http://localhost:{port}/blip2itm"

    def cosine(self, image: np.ndarray, txt: str) -> float:
        print(f"BLIP2ITMClient.cosine: {image.shape}, {txt}")
        response = send_request(self.url, image=image, txt=txt)
        return float(response["response"])
    
    def get_image_embedding(self, image: np.ndarray) -> np.ndarray:
        print(f"BLIP2ITMClient.get_image_embedding: {image.shape}")
        response = send_request(self.url + "/embedding", image=image)
        return np.array(response["response"])
    
    def cosine_with_embedding(self, image: np.ndarray, embedding: np.ndarray) -> float:
        print(f"BLIP2ITMClient.cosine_with_embedding: {image.shape}, {embedding.shape}")
        response = send_request(self.url + "/cosine_embedding", image=image, embedding=embedding.tolist())
        return float(response["response"])


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=12182)
    args = parser.parse_args()

    print("Loading model...")

    class BLIP2ITMServer(ServerMixin, BLIP2ITM):
        def process_payload(self, payload: dict) -> dict:
            # Extract path from the URL
            path = self.path.strip("/")
            
            # Process based on endpoint
            if path == "blip2itm":
                image = str_to_image(payload["image"])
                return {"response": self.cosine(image, payload["txt"])}
            elif path == "blip2itm/embedding":
                image = str_to_image(payload["image"])
                embedding = self.get_image_embedding(image)
                return {"response": embedding.tolist()}
            elif path == "blip2itm/cosine_embedding":
                image = str_to_image(payload["image"])
                embedding = np.array(payload["embedding"])
                return {"response": self.cosine_with_embedding(image, embedding)}
            else:
                return {"error": f"Unknown endpoint: {path}"}

    blip = BLIP2ITMServer()
    print("Model loaded!")
    print(f"Hosting on port {args.port}...")
    host_model(blip, name="blip2itm", port=args.port)
