import hashlib
import os
import random
from typing import TYPE_CHECKING, Any, List, Optional, Union

import numpy as np
from gym import spaces
from habitat.sims.habitat_simulator.habitat_simulator import HabitatSim
from habitat.core.embodied_task import EmbodiedTask
from habitat.core.registry import registry
from habitat.core.simulator import RGBSensor, Sensor, SensorTypes, Simulator
from habitat.core.utils import try_cv2_import
from habitat.tasks.nav.nav import NavigationEpisode

from vlfm.task.goat_task import GoatEpisode

cv2 = try_cv2_import()


from vlfm.utils.utils import load_pickle

if TYPE_CHECKING:
    from omegaconf import DictConfig
from icecream import ic

@registry.register_sensor
class ClipObjectGoalSensor(Sensor):
    r"""A sensor for Object Goal specification as observations which is used in
    ObjectGoal Navigation. The goal is expected to be specified by object_id or
    semantic category id, and we will generate the prompt corresponding to it
    so that it's usable by CLIP's text encoder.
    Args:
        sim: a reference to the simulator for calculating task observations.
        config: a config for the ObjectGoalPromptSensor sensor. Can contain field
            GOAL_SPEC that specifies which id use for goal specification,
            GOAL_SPEC_MAX_VAL the maximum object_id possible used for
            observation space definition.
        dataset: a Object Goal navigation dataset that contains dictionaries
        of categories id to text mapping.
    """

    cls_uuid: str = "clip_objectgoal"

    def __init__(
        self,
        *args: Any,
        config: "DictConfig",
        **kwargs: Any,
    ):
        self.cache = load_pickle(config.cache)
        super().__init__(config=config)

    def _get_uuid(self, *args: Any, **kwargs: Any) -> str:
        return self.cls_uuid

    def _get_sensor_type(self, *args: Any, **kwargs: Any):
        return SensorTypes.SEMANTIC

    def _get_observation_space(self, *args: Any, **kwargs: Any):
        return spaces.Box(
            low=-np.inf, high=np.inf, shape=(1024,), dtype=np.float32
        )

    def get_observation(
        self,
        observations,
        *args: Any,
        episode: Any,
        task: EmbodiedTask,
        **kwargs: Any,
    ) -> Optional[int]:
        dummy_embedding = np.zeros((1024,), dtype=np.float32)
        try:
            if isinstance(episode, GoatEpisode):
                # print(
                #     "GoatEpisode: {} - {}".format(
                #         episode.tasks[task.active_subtask_idx],
                #         isinstance(episode, GoatEpisode),
                #     )
                # )
                if task.active_subtask_idx < len(episode.tasks):
                    if episode.tasks[task.active_subtask_idx][1] == "object":
                        category = episode.tasks[task.active_subtask_idx][0]
                    else:
                        return dummy_embedding
                else:
                    return dummy_embedding
            else:
                category = (
                    episode.object_category
                    if hasattr(episode, "object_category")
                    else ""
                )
            if category not in self.cache:
                print("ObjectGoal Missing category: {}".format(category))
            # print("ObjectGoal Found category: {}".format(category))
        except Exception as e:
            print("Object goal exception ", e)
        return self.cache[category]


@registry.register_sensor
class ClipImageGoalSensor(Sensor):
    cls_uuid: str = "clip_imagegoal"

    def __init__(
        self,
        sim: "HabitatSim",
        config: "DictConfig",
        *args: Any,
        **kwargs: Any,
    ):
        self._sim = sim
        sensors = self._sim.sensor_suite.sensors
        rgb_sensor_uuids = [
            uuid
            for uuid, sensor in sensors.items()
            if isinstance(sensor, RGBSensor)
        ]
        if len(rgb_sensor_uuids) != 1:
            raise ValueError(
                "ImageGoalNav requires one RGB sensor,"
                f" {len(rgb_sensor_uuids)} detected"
            )
        (self._rgb_sensor_uuid,) = rgb_sensor_uuids
        super().__init__(config=config)
        self._curr_ep_id = None
        self.image_goal = None

    def _get_uuid(self, *args: Any, **kwargs: Any) -> str:
        return self.cls_uuid

    def _get_sensor_type(self, *args: Any, **kwargs: Any):
        return SensorTypes.COLOR

    def _get_observation_space(self, *args: Any, **kwargs: Any):
        return self._sim.sensor_suite.observation_spaces.spaces[
            self._rgb_sensor_uuid
        ]

    def _reset(self, episode):
        self._curr_ep_id = episode.episode_id
        sampled_goal = random.choice(episode.goals)
        sampled_viewpoint = random.choice(sampled_goal.view_points)
        observations = self._sim.get_observations_at(
            position=sampled_viewpoint.agent_state.position,
            rotation=sampled_viewpoint.agent_state.rotation,
            keep_agent_at_new_pose=False,
        )
        assert observations is not None
        self.image_goal = observations["rgb"]
        # Mutate the episode
        episode.goals = [sampled_goal]

    def get_observation(
        self,
        observations,
        episode: Any,
        *args: Any,
        **kwargs: Any,
    ) -> np.ndarray:
        if self.image_goal is None or self._curr_ep_id != episode.episode_id:
            self._reset(episode)
        assert self.image_goal is not None
        return self.image_goal


@registry.register_sensor
class ClipGoalSelectorSensor(Sensor):
    cls_uuid: str = "clip_goal_selector"

    def __init__(
        self,
        config: "DictConfig",
        *args: Any,
        **kwargs: Any,
    ):
        super().__init__(config=config)
        self._image_sampling_prob = config.image_sampling_probability
        self._curr_ep_id = None
        self._use_image_goal = True

    def _get_uuid(self, *args: Any, **kwargs: Any) -> str:
        return self.cls_uuid

    def _get_sensor_type(self, *args: Any, **kwargs: Any):
        return SensorTypes.TENSOR

    def _get_observation_space(self, *args: Any, **kwargs: Any):
        return spaces.Box(
            low=0,
            high=1,
            shape=(1,),
            dtype=np.bool,
        )

    def _reset(self, episode):
        self._curr_ep_id = episode.episode_id
        self._use_image_goal = random.random() < self._image_sampling_prob

    def get_observation(
        self,
        observations,
        episode: Any,
        *args: Any,
        **kwargs: Any,
    ) -> np.ndarray:
        if self._curr_ep_id != episode.episode_id:
            self._reset(episode)
        return np.array([self._use_image_goal], dtype=np.bool)


@registry.register_sensor
class ImageGoalRotationSensor(Sensor):
    r"""Sensor for ImageGoal observations which are used in ImageGoal Navigation.
    RGBSensor needs to be one of the Simulator sensors.
    This sensor return the rgb image taken from the goal position to reach with
    random rotation.
    Args:
        sim: reference to the simulator for calculating task observations.
        config: config for the ImageGoal sensor.
    """

    cls_uuid: str = "image_goal_rotation"

    def __init__(
        self, *args: Any, sim: Simulator, config: "DictConfig", **kwargs: Any
    ):
        self._sim = sim
        sensors = self._sim.sensor_suite.sensors
        rgb_sensor_uuids = [
            uuid
            for uuid, sensor in sensors.items()
            if isinstance(sensor, RGBSensor)
        ]
        if len(rgb_sensor_uuids) != 1:
            raise ValueError(
                "ImageGoalNav requires one RGB sensor,"
                f" {len(rgb_sensor_uuids)} detected"
            )

        (self._rgb_sensor_uuid,) = rgb_sensor_uuids
        self._current_episode_id: Optional[str] = None
        self._current_image_goal = None
        super().__init__(config=config)

    def _get_uuid(self, *args: Any, **kwargs: Any) -> str:
        return self.cls_uuid

    def _get_sensor_type(self, *args: Any, **kwargs: Any):
        return SensorTypes.PATH

    def _get_observation_space(self, *args: Any, **kwargs: Any):
        return self._sim.sensor_suite.observation_spaces.spaces[
            self._rgb_sensor_uuid
        ]

    def _get_pointnav_episode_image_goal(self, episode: NavigationEpisode):
        goal_position = np.array(episode.goals[0].position, dtype=np.float32)

        # Add rotation to episode
        if self.config.sample_angle:
            angle = np.random.uniform(0, 2 * np.pi)
        else:
            # to be sure that the rotation is the same for the same episode_id
            # since the task is currently using pointnav Dataset.
            seed = abs(hash(episode.episode_id)) % (2**32)
            rng = np.random.RandomState(seed)
            angle = rng.uniform(0, 2 * np.pi)
        source_rotation = [0, np.sin(angle / 2), 0, np.cos(angle / 2)]
        episode.goals[0].rotation = source_rotation

        goal_observation = self._sim.get_observations_at(
            position=goal_position.tolist(), rotation=source_rotation
        )
        return goal_observation[self._rgb_sensor_uuid]

    def get_observation(
        self,
        *args: Any,
        observations,
        episode: NavigationEpisode,
        **kwargs: Any,
    ):
        episode_uniq_id = f"{episode.scene_id} {episode.episode_id}"
        if episode_uniq_id == self._current_episode_id:
            return self._current_image_goal

        self._current_image_goal = self._get_pointnav_episode_image_goal(
            episode
        )
        self._current_episode_id = episode_uniq_id

        return self._current_image_goal


@registry.register_sensor
class CurrentEpisodeUUIDSensor(Sensor):
    r"""Sensor for current episode uuid observations.
    Args:
        sim: reference to the simulator for calculating task observations.
        config: config for the ImageGoal sensor.
    """

    cls_uuid: str = "current_episode_uuid"

    def __init__(
        self, *args: Any, sim: Simulator, config: "DictConfig", **kwargs: Any
    ):
        self._sim = sim
        self._current_episode_id: Optional[str] = None

        super().__init__(config=config)

    def _get_uuid(self, *args: Any, **kwargs: Any) -> str:
        return self.cls_uuid

    def _get_sensor_type(self, *args: Any, **kwargs: Any):
        return SensorTypes.TENSOR

    def _get_observation_space(self, *args: Any, **kwargs: Any):
        return spaces.Box(
            low=np.iinfo(np.int64).min,
            high=np.iinfo(np.int64).max,
            shape=(1,),
            dtype=np.int64,
        )

    def get_observation(
        self,
        *args: Any,
        observations,
        episode: NavigationEpisode,
        **kwargs: Any,
    ):
        episode_uniq_id = f"{episode.scene_id} {episode.episode_id}"
        episode_uuid = (
            int(hashlib.sha1(episode_uniq_id.encode("utf-8")).hexdigest(), 16)
            % 10**8
        )
        return episode_uuid


@registry.register_sensor
class LanguageGoalSensor(Sensor):
    r"""A sensor for language goal specification as observations which is used in
    Language Navigation.
    Args:
        sim: a reference to the simulator for calculating task observations.
        config: a config for the ObjectGoalPromptSensor sensor. Can contain field
            GOAL_SPEC that specifies which id use for goal specification,
            GOAL_SPEC_MAX_VAL the maximum object_id possible used for
            observation space definition.
        dataset: a Object Goal navigation dataset that contains dictionaries
        of categories id to text mapping.
    """

    cls_uuid: str = "language_goal"

    def __init__(
        self,
        *args: Any,
        config: "DictConfig",
        **kwargs: Any,
    ):
        self.cache = load_pickle(config.cache)
        self.embedding_dim = config.embedding_dim
        super().__init__(config=config)

    def _get_uuid(self, *args: Any, **kwargs: Any) -> str:
        return self.cls_uuid

    def _get_sensor_type(self, *args: Any, **kwargs: Any):
        return SensorTypes.SEMANTIC

    def _get_observation_space(self, *args: Any, **kwargs: Any):
        return spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(self.embedding_dim,),
            dtype=np.float32,
        )

    def get_observation(
        self,
        observations,
        *args: Any,
        episode: Any,
        task: EmbodiedTask,
        **kwargs: Any,
    ) -> Optional[int]:
        uuid = ""

        try:
            dummy_embedding = np.zeros((self.embedding_dim,), dtype=np.float32)
            if isinstance(episode, GoatEpisode):
                # print(
                #     "Lang GoatEpisode: {} - {}".format(
                #         episode.tasks[task.active_subtask_idx],
                #         isinstance(episode, GoatEpisode),
                #     )
                # )
                if task.active_subtask_idx < len(episode.tasks):
                    if (
                        episode.tasks[task.active_subtask_idx][1]
                        == "description"
                    ):
                        # print("not retur lang")
                        instance_id = episode.tasks[task.active_subtask_idx][2]
                        # print("instance id", instance_id)
                        # print(
                        #     "episode goals",
                        #     [
                        #         list(g.keys())
                        #         for g in episode.goals[task.active_subtask_idx]
                        #     ],
                        # )
                        goal = [
                            g
                            for g in episode.goals[task.active_subtask_idx]
                            if g["object_id"] == instance_id
                        ]
                        uuid = goal[0]["lang_desc"].lower()
                    else:
                        return dummy_embedding
                else:
                    return dummy_embedding
            else:
                uuid = episode.instructions[0].lower()
                first_3_words = [
                    "prefix: instruction: go",
                    "instruction: find the",
                    "instruction: go to",
                    "api_failure",
                    "instruction: locate the",
                ]
                for prefix in first_3_words:
                    uuid = uuid.replace(prefix, "")
                    uuid = uuid.replace("\n", " ")
                uuid = uuid.strip()

            if self.cache.get(uuid) is None:
                print("Lang Missing category: {}".format(uuid))
        except Exception as e:
            print("Language goal exception ", e)
        return self.cache[uuid]


@registry.register_sensor
class CacheImageGoalSensor(Sensor):
    r"""A sensor for Image goal specification as observations which is used in IIN.
    Args:
        sim: a reference to the simulator for calculating task observations.
        config: a config for the ObjectGoalPromptSensor sensor. Can contain field
            GOAL_SPEC that specifies which id use for goal specification,
            GOAL_SPEC_MAX_VAL the maximum object_id possible used for
            observation space definition.
        dataset: a Object Goal navigation dataset that contains dictionaries
        of categories id to text mapping.
    """

    cls_uuid: str = "cache_instance_imagegoal"

    def __init__(
        self,
        *args: Any,
        config: "DictConfig",
        **kwargs: Any,
    ):
        self.cache_base_dir = config.cache
        self.image_encoder = config.image_cache_encoder
        self.cache = None
        self._current_scene_id = ""
        self._current_episode_id = ""
        self._current_episode_image_goal = np.zeros((1024,), dtype=np.float32)
        super().__init__(config=config)

    def _get_uuid(self, *args: Any, **kwargs: Any) -> str:
        return self.cls_uuid

    def _get_sensor_type(self, *args: Any, **kwargs: Any):
        return SensorTypes.SEMANTIC

    def _get_observation_space(self, *args: Any, **kwargs: Any):
        return spaces.Box(
            low=-np.inf, high=np.inf, shape=(1024,), dtype=np.float32
        )

    def get_observation(
        self,
        observations,
        *args: Any,
        episode: Any,
        task: EmbodiedTask,
        **kwargs: Any,
    ) -> Optional[int]:
        episode_id = f"{episode.scene_id}_{episode.episode_id}"
        if self._current_scene_id != episode.scene_id:
            self._current_scene_id = episode.scene_id
            scene_id = episode.scene_id.split("/")[-1].split(".")[0]

            suffix = "embedding.pkl"
            if self.image_encoder != "":
                suffix = "{}_iin_{}".format(self.image_encoder, suffix)
            if isinstance(episode, GoatEpisode):
                suffix = suffix.replace("iin", "goat")

            print(
                "Cache dir: {}".format(
                    os.path.join(self.cache_base_dir, f"{scene_id}_{suffix}")
                )
            )
            self.cache = load_pickle(
                os.path.join(self.cache_base_dir, f"{scene_id}_{suffix}")
            )

        try:
            if self._current_episode_id != episode_id:
                self._current_episode_id = episode_id

                dummy_embedding = np.zeros((1024,), dtype=np.float32)
                if isinstance(episode, GoatEpisode):
                    if task.active_subtask_idx < len(episode.tasks):
                        if episode.tasks[task.active_subtask_idx][1] == "image":
                            instance_id = episode.tasks[
                                task.active_subtask_idx
                            ][2]
                            curent_task = episode.tasks[task.active_subtask_idx]
                            scene_id = episode.scene_id.split("/")[-1].split(
                                "."
                            )[0]

                            uuid = "{}_{}".format(scene_id, instance_id)

                            self._current_episode_image_goal = self.cache[
                                "{}_{}".format(scene_id, instance_id)
                            ][curent_task[-1]]["embedding"]
                        else:
                            self._current_episode_image_goal = dummy_embedding
                    else:
                        self._current_episode_image_goal = dummy_embedding
                else:
                    self._current_episode_image_goal = self.cache[
                        episode.goal_key
                    ][episode.goal_image_id]["embedding"]
        except Exception as e:
            print("Image goal exception ", e)
            raise e

        return self._current_episode_image_goal


@registry.register_sensor
class GoatCurrentSubtaskSensor(Sensor):
    r"""A sensor that returns the current subtask type ID in GOAT multi-task navigation.

    This sensor maps the current active subtask type (e.g., "object", "description", "image")
    to a numerical ID, enabling the policy to determine what type of navigation task
    it should currently execute.

    Args:
        config: A config for the GoatCurrentSubtaskSensor. Must contain field
            sub_task_type that specifies the list of supported task types
            (e.g., ["object", "description", "image"]).

    Returns:
        int: The index of the current subtask type in the sub_task_type list,
             or len(sub_task_type) if no active subtask or invalid index.
    """

    cls_uuid: str = "current_subtask"

    def __init__(
        self,
        *args: Any,
        config: "DictConfig",
        **kwargs: Any,
    ):
        self.sub_task_type = config.sub_task_type
        super().__init__(config=config)

    def _get_uuid(self, *args: Any, **kwargs: Any) -> str:
        return self.cls_uuid

    def _get_sensor_type(self, *args: Any, **kwargs: Any):
        return SensorTypes.SEMANTIC

    def _get_observation_space(self, *args: Any, **kwargs: Any):
        return spaces.Box(
            low=0, high=len(self.sub_task_type) + 1, shape=(1,), dtype=np.int32
        )

    def get_observation(
        self,
        observations,
        *args: Any,
        episode: Any,
        task: EmbodiedTask,
        **kwargs: Any,
    ) -> Optional[int]:
        current_subtask = task.active_subtask_idx
        current_subtask_id = len(self.sub_task_type)
        if current_subtask < len(episode.tasks):
            current_subtask_id = self.sub_task_type.index(
                episode.tasks[current_subtask][1]
            )

        return current_subtask_id


@registry.register_sensor
class GoatGoalSensor(Sensor):
    r"""A sensor for GOAT goals that returns the actual target format based on task type.

    This sensor returns different types of goals depending on the current subtask:
    - For "object" tasks: returns the object category name encoded as numpy uint8 array
    - For "description" tasks: returns the language description encoded as numpy uint8 array
    - For "image" tasks: returns the actual RGB image as numpy array

    Args:
        sim: Reference to the simulator for extracting target images.
        config: Configuration for the sensor.

    Returns:
        np.ndarray: Either encoded text (uint8 array) for object/description tasks,
                   or RGB image array for image tasks.
    """

    cls_uuid: str = "goat_subtask_goal"

    def __init__(
        self,
        sim: "HabitatSim",
        config: "DictConfig",
        *args: Any,
        **kwargs: Any,
    ):
        self._sim = sim
        # Get RGB sensor UUID for image goal extraction
        sensors = self._sim.sensor_suite.sensors
        rgb_sensor_uuids = [
            uuid
            for uuid, sensor in sensors.items()
            if isinstance(sensor, RGBSensor)
        ]
        if len(rgb_sensor_uuids) != 1:
            raise ValueError(
                "GoatGoalSensor requires one RGB sensor,"
                f" {len(rgb_sensor_uuids)} detected"
            )
        (self._rgb_sensor_uuid,) = rgb_sensor_uuids

        self._current_scene_id = ""
        self._current_episode_id = ""
        self._current_subtask_idx = -1
        self._cached_image_goal = None
        super().__init__(config=config)

    def _get_uuid(self, *args: Any, **kwargs: Any) -> str:
        return self.cls_uuid

    def _get_sensor_type(self, *args: Any, **kwargs: Any):
        return SensorTypes.SEMANTIC

    def _get_observation_space(self, *args: Any, **kwargs: Any):
        # Return a very flexible space since we return different types of data
        # Use Dict space to handle the variability, or just a loose Box space
        from gym.spaces import Dict
        return spaces.Box(
            low=0,
            high=255,
            shape=(1,),
            dtype=np.uint8
        )

    def _extract_image_goal(self, episode: Any, task: Any) -> np.ndarray:
        """Extract target image for image-type tasks, using method similar to save_image_goals.py"""
        instance_id = episode.tasks[task.active_subtask_idx][2]
        image_id = episode.tasks[task.active_subtask_idx][3]

        # Find the goal corresponding to the instance_id
        goal_objects = [
            g for g in episode.goals[task.active_subtask_idx]
            if g["object_id"] == instance_id
        ]

        # Get the first matching goal
        goal = goal_objects[0]

        if "image_goals" in goal and len(goal["image_goals"]) > image_id:
            img_goal = goal["image_goals"][image_id]
            ic(img_goal)

            # Use the exact parameters from the configuration file
            img_goal_params = img_goal.copy()

            # Extract hfov from config (use original value)
            original_hfov = img_goal_params.get("hfov", 90)
            ic(f"Using original hfov from config: {original_hfov}")

            # Extract dimensions from config (use original value)
            if "image_dimensions" in img_goal_params:
                original_dimensions = img_goal_params["image_dimensions"]
                img_goal_params["dimensions"] = original_dimensions
                ic(f"Using original dimensions from config: {original_dimensions}")
            elif "dimensions" in img_goal_params:
                original_dimensions = img_goal_params["dimensions"]
                ic(f"Using original dimensions from config: {original_dimensions}")
            else:
                # Only set default if no dimensions specified
                img_goal_params["dimensions"] = [512, 512]
                ic("No dimensions in config, using default [512, 512]")

            # Use direct observation method with custom sensor parameters
            try:
                # Create a temporary sensor with the specified parameters
                rgb_image = self._get_observation_with_custom_params(
                    position=img_goal_params["position"],
                    rotation=img_goal_params["rotation"],
                    hfov=original_hfov,
                    dimensions=img_goal_params.get("dimensions", [512, 512])
                )

                if rgb_image is not None and np.mean(rgb_image) > 10:
                    ic(f"Successfully extracted image using custom sensor with original config")
                    return rgb_image
            except Exception as e:
                ic(f"Custom sensor observation failed: {e}")

        # If we reach here, return a black image as fallback
        print(f"Cannot extract valid image for goal: {goal}")
        rgb_space = self._sim.sensor_suite.observation_spaces.spaces[self._rgb_sensor_uuid]
        return np.zeros(rgb_space.shape, dtype=rgb_space.dtype)

    def get_observation(
        self,
        observations,
        *args: Any,
        episode: Any,
        task: Any,
        **kwargs: Any,
    ) -> np.ndarray:
        episode_id = f"{episode.scene_id}_{episode.episode_id}"
        ic(episode_id)
        ic(episode.tasks)

        if self._current_scene_id != episode.scene_id:
            self._current_scene_id = episode.scene_id
            scene_id = episode.scene_id.split("/")[-1].split(".")[0]

        ic(task.active_subtask_idx)
        ic(episode.tasks[task.active_subtask_idx])

        if task.active_subtask_idx < len(episode.tasks):
            task_type = episode.tasks[task.active_subtask_idx][1]

            if task_type == "object":
                # Return object category as numpy array (encoded from string)
                category = episode.tasks[task.active_subtask_idx][0]
                byte_array = bytearray(category.encode('utf-8'))
                return np.array(byte_array, dtype=np.uint8)

            elif task_type == "description":
                # Return language description as numpy array (encoded from string)
                instance_id = episode.tasks[task.active_subtask_idx][2]
                goal = [
                    g
                    for g in episode.goals[task.active_subtask_idx]
                    if g["object_id"] == instance_id
                ]
                lang_desc = goal[0]["lang_desc"].lower()
                byte_array = bytearray(lang_desc.encode('utf-8'))
                return np.array(byte_array, dtype=np.uint8)

            elif task_type == "image":
                # Return actual RGB image as numpy array
                # Cache based on both episode_id and subtask_idx since GOAT has multiple subtasks per episode
                current_subtask_idx = task.active_subtask_idx
                if (self._current_episode_id != episode_id or
                    self._current_subtask_idx != current_subtask_idx or
                    self._cached_image_goal is None
                   ):
                    self._current_episode_id = episode_id
                    self._current_subtask_idx = current_subtask_idx
                    self._cached_image_goal = self._extract_image_goal(episode, task)
                return self._cached_image_goal

            else:
                raise NotImplementedError(f"Unknown task type: {task_type}")

        # Default fallback - return empty numpy array
        return np.array([], dtype=np.uint8)

    def _get_observation_with_custom_params(
        self,
        position: List[float],
        rotation: List[float],
        hfov: float,
        dimensions: List[int]
    ) -> Optional[np.ndarray]:
        """Get RGB observation with custom camera parameters by temporarily modifying existing sensor"""
        try:
            import habitat_sim

            # Save current agent state
            current_state = self._sim.get_agent_state()

            # Get the current RGB sensor
            rgb_sensor = self._sim.sensor_suite.sensors[self._rgb_sensor_uuid]

            # Save original sensor configuration
            original_config = rgb_sensor.config
            original_hfov = getattr(original_config, 'hfov', 90)
            original_height = getattr(original_config, 'height', 480)
            original_width = getattr(original_config, 'width', 640)

            # Temporarily modify the sensor configuration
            rgb_sensor.config.hfov = hfov
            rgb_sensor.config.height = dimensions[1]  # height
            rgb_sensor.config.width = dimensions[0]   # width

            # Reconfigure the simulator with new sensor settings
            try:
                # Set agent to the desired position and rotation
                success = self._sim.set_agent_state(
                    position, rotation, reset_sensors=True  # Reset sensors to apply new config
                )

                if not success:
                    ic(f"Failed to set agent state to position: {position}")
                    return None

                # Get observation with new parameters
                observations = self._sim.get_sensor_observations()
                rgb_image = observations.get(self._rgb_sensor_uuid)

                if rgb_image is not None:
                    # Remove alpha channel if present
                    if rgb_image.shape[-1] == 4:
                        rgb_image = rgb_image[:, :, :3]
                    ic(f"Custom sensor observation successful: shape={rgb_image.shape}, hfov={hfov}, dimensions={dimensions}")
                    return rgb_image
                else:
                    ic("Custom sensor observation returned None")
                    return None

            finally:
                # Always restore original sensor configuration
                rgb_sensor.config.hfov = original_hfov
                rgb_sensor.config.height = original_height
                rgb_sensor.config.width = original_width

                # Restore original agent state
                self._sim.set_agent_state(
                    current_state.position,
                    current_state.rotation,
                    reset_sensors=True  # Reset sensors to apply original config
                )

        except Exception as e:
            ic(f"Error in custom sensor observation: {e}")
            # Try to restore original state in case of error
            try:
                self._sim.set_agent_state(
                    current_state.position,
                    current_state.rotation,
                    reset_sensors=True
                )
            except:
                pass
            return None


@registry.register_sensor
class GoatMultiGoalSensor(Sensor):
    r"""A sensor for Goat goals"""

    cls_uuid: str = "goat_subtask_multi_goal"

    def __init__(
        self,
        *args: Any,
        config: "DictConfig",
        **kwargs: Any,
    ):
        self.image_cache_base_dir = config.image_cache
        self.image_encoder = config.image_cache_encoder
        self.image_cache = None
        self.language_cache = load_pickle(config.language_cache)
        self.object_cache = load_pickle(config.object_cache)
        self._current_scene_id = ""
        self._current_episode_id = ""
        self._current_episode_image_goal = None
        super().__init__(config=config)

    def _get_uuid(self, *args: Any, **kwargs: Any) -> str:
        return self.cls_uuid

    def _get_sensor_type(self, *args: Any, **kwargs: Any):
        return SensorTypes.SEMANTIC

    def _get_observation_space(self, *args: Any, **kwargs: Any):
        return spaces.Box(
            low=-np.inf, high=np.inf, shape=(1024 * 3,), dtype=np.float32
        )

    def get_observation(
        self,
        observations,
        *args: Any,
        episode: Any,
        task: Any,
        **kwargs: Any,
    ) -> np.ndarray:
        episode_id = f"{episode.scene_id}_{episode.episode_id}"

        if self._current_scene_id != episode.scene_id:
            self._current_scene_id = episode.scene_id
            scene_id = episode.scene_id.split("/")[-1].split(".")[0]
            self.image_cache = load_pickle(
                os.path.join(
                    self.image_cache_base_dir,
                    f"{scene_id}_{self.image_encoder}_embedding.pkl",
                )
            )

        output_embedding = np.zeros((1024 * 3,), dtype=np.float32)
        scene_id = episode.scene_id.split("/")[-1].split(".")[0]

        task_type = "none"
        if task.active_subtask_idx < len(episode.tasks):
            if episode.tasks[task.active_subtask_idx][1] == "object":
                category = episode.tasks[task.active_subtask_idx][0]
                obj_embedding = self.object_cache[category]
                output_embedding = np.concatenate(
                    (obj_embedding, obj_embedding, obj_embedding)
                )
                task_type = "object"
            elif episode.tasks[task.active_subtask_idx][1] == "description":
                instance_id = episode.tasks[task.active_subtask_idx][2]
                goal = [
                    g
                    for g in episode.goals[task.active_subtask_idx]
                    if g["object_id"] == instance_id
                ]
                uuid = goal[0]["lang_desc"].lower()
                lang_embedding = self.language_cache[uuid]

                uuid = "{}_{}".format(scene_id, instance_id)
                random_idx = random.choice(
                    range(
                        len(
                            self.image_cache[
                                "{}_{}".format(scene_id, instance_id)
                            ]
                        )
                    ),
                )

                img_embedding = self.image_cache[
                    "{}_{}".format(scene_id, instance_id)
                ][random_idx]["embedding"]

                category = episode.tasks[task.active_subtask_idx][0]
                cat_embedding = self.object_cache[category]

                output_embedding = np.concatenate(
                    (lang_embedding, img_embedding, cat_embedding)
                )
                task_type = "lang"
            elif episode.tasks[task.active_subtask_idx][1] == "image":
                instance_id = episode.tasks[task.active_subtask_idx][2]
                curent_task = episode.tasks[task.active_subtask_idx]
                scene_id = episode.scene_id.split("/")[-1].split(".")[0]

                uuid = "{}_{}".format(scene_id, instance_id)

                img_embedding = self.image_cache[
                    "{}_{}".format(scene_id, instance_id)
                ][curent_task[-1]]["embedding"]

                category = episode.tasks[task.active_subtask_idx][0]
                cat_embedding = self.object_cache[category]

                goal = [
                    g
                    for g in episode.goals[task.active_subtask_idx]
                    if g["object_id"] == instance_id
                ]
                uuid = goal[0]["lang_desc"]
                if uuid is not None:
                    uuid = uuid.lower()
                    lang_embedding = self.language_cache[uuid]
                else:
                    lang_embedding = cat_embedding

                output_embedding = np.concatenate(
                    (lang_embedding, img_embedding, cat_embedding)
                )

                task_type = "image"
            else:
                raise NotImplementedError
        return output_embedding
