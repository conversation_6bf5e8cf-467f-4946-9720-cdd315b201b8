import importlib
from typing import TYPE_CHECKING, Optional, Type

import gym
import numpy as np

import habitat
from habitat import Dataset
from habitat.gym.gym_wrapper import HabGymWrapper
from habitat.core.environments import RLTaskEnv
from habitat.core.registry import registry

if TYPE_CHECKING:
    from omegaconf import DictConfig

class GoatRLEnv(RLTaskEnv):
    def __init__(self, config: "DictConfig", dataset: Optional[Dataset] = None):
        super().__init__(config, dataset)
        # self._reward_measure_name = self.config.task.reward_measure
        # self._success_measure_name = self.config.task.success_measure
        # assert (
        #     self._reward_measure_name is not None
        # ), "The key task.reward_measure cannot be None"
        # assert (
        #     self._success_measure_name is not None
        # ), "The key task.success_measure cannot be None"

    def reset(self, *args, **kwargs):
        try:
            observations = super().reset(*args, **kwargs)
        except TypeError:
            # 处理旧版本的 Habitat API，它不支持 return_info 参数
            if 'return_info' in kwargs:
                return_info = kwargs.pop('return_info')
                observations = super().reset(*args, **kwargs)
                if return_info:
                    return observations, {}
            else:
                observations = super().reset(*args, **kwargs)
        return observations

    def step(self, *args, **kwargs):
        return super().step(*args, **kwargs)

    # def get_reward_range(self):
    #     # We don't know what the reward measure is bounded by
    #     return (-np.inf, np.inf)

    def get_reward(self, observations):
        current_measure = self._env.get_metrics()[self._reward_measure_name]
        reward = self.config.task.slack_reward

        reward += current_measure

        if self._episode_success()["composite_success"] == 1.0:
            reward += self.config.task.success_reward

        return reward

    # def _episode_success(self):
    #     return self._env.get_metrics()[self._success_measure_name]

    def get_done(self, observations):
        done = False
        if self._env.episode_over:
            done = True
        if (
            self.config.task.end_on_success
            and self._episode_success()["composite_success"] == 1.0
        ):
            done = True
        return done

    # def get_info(self, observations):
    #     return self.habitat_env.get_metrics()


class GoatZeroShotEnv(habitat.core.env.Env):
    """零样本导航环境类"""
    def __init__(self, config: "DictConfig", dataset: Optional[Dataset] = None):
        super().__init__(config, dataset)
        self._success_measure_name = self.config.task.success_measure
        assert (
            self._success_measure_name is not None
        ), "The key task.success_measure cannot be None"

    def reset(self, *args, **kwargs):
        """重置环境状态"""
        try:
            observations = super().reset(*args, **kwargs)
        except TypeError:
            # 处理旧版本的 Habitat API，它不支持 return_info 参数
            if 'return_info' in kwargs:
                return_info = kwargs.pop('return_info')
                observations = super().reset(*args, **kwargs)
                if return_info:
                    return observations, {}
            else:
                observations = super().reset(*args, **kwargs)
        return observations

    def step(self, action):
        """执行动作并返回下一个状态"""
        observations = self._env.step(action)
        return observations

    def _episode_success(self):
        """检查是否成功完成任务"""
        return self._env.get_metrics()[self._success_measure_name]

    def get_done(self, observations):
        """检查episode是否结束"""
        done = False
        if self._env.episode_over:
            done = True
        if (
            self.config.task.end_on_success
            and self._episode_success()["composite_success"] == 1.0
        ):
            done = True
        return done

    def get_info(self, observations):
        """获取环境信息"""
        return self.habitat_env.get_metrics()


@habitat.registry.register_env(name="GymGoatEnv")
class GymGoatEnv(gym.Wrapper):
    """
    A registered environment that wraps a RLTaskEnv with the HabGymWrapper
    to use the default gym API.
    """

    def __init__(self, config: "DictConfig", dataset: Optional[Dataset] = None):
        base_env = GoatRLEnv(config=config, dataset=dataset)
        env = HabGymWrapper(base_env)
        super().__init__(env)


@habitat.registry.register_env(name="GymGoatZeroShotEnv")
class GymGoatZeroShotEnv(gym.Wrapper):
    """零样本环境的Gym包装器"""
    def __init__(self, config: "DictConfig", dataset: Optional[Dataset] = None):
        base_env = GoatZeroShotEnv(config=config, dataset=dataset)
        env = HabGymWrapper(base_env)
        super().__init__(env)
